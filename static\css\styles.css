/* Aesthetic Portfolio CSS */

:root {
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #ffffff;
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-dark: #111827;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    overflow-x: hidden;
    background-color: var(--bg-primary);
}

/* Typography */
.brand-logo {
    text-decoration: none;
}

.brand-text {
    font-family: 'Playfair Display', serif;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--primary-color);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.title-underline {
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    margin: 0 auto 1rem;
    border-radius: 2px;
}

/* Navigation */
.aesthetic-nav {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.aesthetic-nav .nav-link {
    color: var(--text-primary) !important;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    margin: 0 0.25rem;
}

.aesthetic-nav .nav-link:hover {
    color: var(--primary-color) !important;
    background-color: rgba(99, 102, 241, 0.1);
}

.aesthetic-nav .nav-link.active {
    color: var(--primary-color) !important;
    background-color: rgba(99, 102, 241, 0.1);
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    color: var(--text-light);
    z-index: 2;
    position: relative;
}

.hero-greeting {
    font-size: 1.125rem;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
    font-weight: 400;
}

.hero-title {
    font-family: 'Playfair Display', serif;
    font-size: 4rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.typing-text {
    background: linear-gradient(135deg, #ffffff, #f0f9ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    border-right: 3px solid rgba(255, 255, 255, 0.8);
    animation: typing 3s steps(20) 1s 1 normal both, blink 1s infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    50% { border-color: transparent; }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.7;
    max-width: 500px;
}

.btn-primary-custom {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border: none;
    color: var(--text-light);
    font-weight: 600;
    padding: 14px 32px;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    box-shadow: var(--shadow-md);
}

.btn-primary-custom:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--text-light);
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

.btn-outline-custom {
    border: 2px solid rgba(255, 255, 255, 0.3);
    color: rgba(255, 255, 255, 0.9);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 12px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-outline-custom:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    transform: translateY(-2px);
    border-color: rgba(255, 255, 255, 0.5);
}

.profile-container {
    position: relative;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-bg {
    width: 300px;
    height: 300px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    border-radius: 50%;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    animation: float 6s ease-in-out infinite;
}

.floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
}

.element {
    position: absolute;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    backdrop-filter: blur(10px);
}

.element-1 {
    width: 20px;
    height: 20px;
    top: 20%;
    left: 20%;
    animation: float 4s ease-in-out infinite;
}

.element-2 {
    width: 15px;
    height: 15px;
    top: 60%;
    right: 15%;
    animation: float 5s ease-in-out infinite reverse;
}

.element-3 {
    width: 25px;
    height: 25px;
    bottom: 30%;
    left: 10%;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
    40% { transform: translateX(-50%) translateY(-10px); }
    60% { transform: translateX(-50%) translateY(-5px); }
}

/* About Section */
.about-section {
    background: var(--bg-secondary);
    position: relative;
}

.about-image {
    position: relative;
    text-align: center;
}

.image-frame {
    position: relative;
    display: inline-block;
}

.image-frame::before {
    content: '';
    position: absolute;
    top: -15px;
    left: -15px;
    right: -15px;
    bottom: -15px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    z-index: -1;
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.decorative-element {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--accent-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    color: white;
    animation: pulse 3s infinite;
    box-shadow: var(--shadow-lg);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.about-title {
    font-family: 'Playfair Display', serif;
    color: var(--text-primary);
    font-size: 2.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.about-text {
    font-size: 1.125rem;
    margin-bottom: 1.5rem;
    color: var(--text-secondary);
    line-height: 1.7;
}

.stats-row {
    display: flex;
    justify-content: space-around;
    margin-top: 3rem;
    gap: 2rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-item h4 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.95rem;
}

/* Skills Section */
.skills-section {
    background: white;
    color: var(--text-primary);
}

.skill-card {
    background: white;
    border-radius: 20px;
    padding: 2.5rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    height: 100%;
}

.skill-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.skill-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.skill-card h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-primary);
}

.skill-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.skill-progress {
    background: var(--bg-secondary);
    height: 6px;
    border-radius: 3px;
    margin-top: 1.5rem;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
    width: 0;
    animation: fillProgress 2s ease-in-out forwards;
    transition: width 0.3s ease;
}

@keyframes fillProgress {
    to { width: var(--width, 0%); }
}

/* Projects Section */
.projects-section {
    background: var(--bg-secondary);
}

.project-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    height: 100%;
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.project-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.project-card:hover .project-image img {
    transform: scale(1.05);
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.9), rgba(139, 92, 246, 0.9));
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-image:hover .project-overlay {
    opacity: 1;
}

.project-overlay .btn {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    border-radius: 8px;
}

.project-content {
    padding: 2rem;
}

.project-content h4 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.project-content p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.project-tags {
    margin-top: 1.5rem;
}

.tag {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 0.375rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    display: inline-block;
}

/* Contact Section */
.contact-section {
    background: linear-gradient(135deg, var(--bg-dark), #1f2937);
    color: var(--text-light);
}

.contact-section .section-title {
    color: var(--text-light);
}

.contact-section .section-subtitle {
    color: rgba(255, 255, 255, 0.8);
}

.contact-form {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    padding: 3rem;
    border-radius: 24px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: var(--shadow-xl);
}

.contact-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--text-light);
    border-radius: 12px;
    padding: 14px 18px;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.contact-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.contact-form .form-control:focus {
    background: rgba(255, 255, 255, 0.15);
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    color: var(--text-light);
    outline: none;
}

.contact-info {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.3s ease;
}

.contact-info:hover {
    transform: translateY(-5px);
}

.contact-info i {
    font-size: 2.5rem;
    color: var(--primary-light);
    margin-bottom: 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.contact-info h5 {
    font-family: 'Playfair Display', serif;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-light);
}

.contact-info p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

/* Footer */
.footer {
    background: var(--bg-dark);
    color: var(--text-light);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer p {
    margin: 0;
    color: rgba(255, 255, 255, 0.8);
}

.social-links a {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.5rem;
    margin-left: 1rem;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.social-links a:hover {
    color: var(--text-light);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .section-title {
        font-size: 2rem;
    }

    .stats-row {
        flex-direction: column;
        gap: 1rem;
    }

    .floating-elements {
        display: none;
    }

    .contact-form {
        padding: 2rem;
    }

    .hero-description {
        font-size: 1rem;
    }

    .about-title {
        font-size: 1.75rem;
    }
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Additional Utility Classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
