/* One Piece Portfolio CSS */

:root {
    --primary-color: #ff6b35;
    --secondary-color: #0f3460;
    --accent-color: #16213e;
    --gold-color: #ffd700;
    --text-light: #ffffff;
    --text-dark: #1a1a2e;
    --ocean-blue: #006994;
    --wave-color: rgba(0, 105, 148, 0.3);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    overflow-x: hidden;
}

/* Typography */
.pirate-brand {
    font-family: 'Pirata One', cursive !important;
    font-size: 1.8rem !important;
    color: var(--gold-color) !important;
}

.section-title {
    font-family: 'Pirata One', cursive;
    font-size: 2.5rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.title-underline {
    width: 80px;
    height: 4px;
    background: linear-gradient(45deg, var(--primary-color), var(--gold-color));
    margin: 0 auto 2rem;
    border-radius: 2px;
}

/* Navigation */
.pirate-nav {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.pirate-nav .nav-link {
    color: var(--text-light) !important;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
}

.pirate-nav .nav-link:hover {
    color: var(--gold-color) !important;
    transform: translateY(-2px);
}

.pirate-nav .nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -5px;
    left: 50%;
    background: var(--gold-color);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.pirate-nav .nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--ocean-blue), var(--secondary-color));
    position: relative;
    overflow: hidden;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.ocean-waves {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 120" preserveAspectRatio="none"><path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25" fill="%23006994"></path><path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5" fill="%23006994"></path><path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z" fill="%23006994"></path></svg>') repeat-x;
    animation: wave 10s linear infinite;
}

@keyframes wave {
    0% { background-position-x: 0; }
    100% { background-position-x: 1200px; }
}

.hero-content {
    color: var(--text-light);
    z-index: 2;
    position: relative;
}

.hero-title {
    font-family: 'Pirata One', cursive;
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.typing-text {
    border-right: 3px solid var(--gold-color);
    animation: typing 3s steps(20) 1s 1 normal both, blink 1s infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    50% { border-color: transparent; }
}

.hero-subtitle {
    font-size: 1.5rem;
    color: var(--gold-color);
    margin-bottom: 1.5rem;
    font-weight: 300;
}

.hero-description {
    font-size: 1.1rem;
    margin-bottom: 2rem;
    opacity: 0.9;
}

.btn-pirate {
    background: linear-gradient(45deg, var(--primary-color), var(--gold-color));
    border: none;
    color: var(--text-light);
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-pirate:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(255, 107, 53, 0.3);
    color: var(--text-light);
}

.btn-outline-pirate {
    border: 2px solid var(--gold-color);
    color: var(--gold-color);
    background: transparent;
    font-weight: 600;
    padding: 10px 28px;
    border-radius: 50px;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-outline-pirate:hover {
    background: var(--gold-color);
    color: var(--secondary-color);
    transform: translateY(-3px);
}

.floating-ship {
    position: absolute;
    top: 20%;
    right: 10%;
    font-size: 4rem;
    color: var(--gold-color);
    animation: float 3s ease-in-out infinite;
}

.treasure-chest {
    position: absolute;
    bottom: 20%;
    right: 20%;
    font-size: 3rem;
    color: var(--primary-color);
    animation: bounce 2s infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    color: var(--text-light);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
}

/* About Section */
.about-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    position: relative;
}

.about-image {
    position: relative;
    text-align: center;
}

.image-frame {
    position: relative;
    display: inline-block;
}

.image-frame::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, var(--primary-color), var(--gold-color));
    border-radius: 50%;
    z-index: -1;
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.devil-fruit {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 2rem;
    color: var(--primary-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.about-title {
    font-family: 'Pirata One', cursive;
    color: var(--secondary-color);
    font-size: 2rem;
    margin-bottom: 1rem;
}

.about-text {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: #666;
}

.stats-row {
    display: flex;
    justify-content: space-around;
    margin-top: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-item h4 {
    font-family: 'Pirata One', cursive;
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-item p {
    color: #666;
    font-weight: 500;
}

/* Skills Section */
.skills-section {
    background: var(--secondary-color);
    color: var(--text-light);
}

.skills-section .section-title {
    color: var(--text-light);
}

.skill-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.skill-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.skill-icon {
    font-size: 3rem;
    color: var(--gold-color);
    margin-bottom: 1rem;
}

.skill-card h4 {
    font-family: 'Pirata One', cursive;
    margin-bottom: 1rem;
    color: var(--text-light);
}

.skill-progress {
    background: rgba(255, 255, 255, 0.2);
    height: 8px;
    border-radius: 4px;
    margin-top: 1rem;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(45deg, var(--primary-color), var(--gold-color));
    border-radius: 4px;
    width: 0;
    animation: fillProgress 2s ease-in-out forwards;
}

@keyframes fillProgress {
    to { width: var(--width, 0%); }
}

/* Projects Section */
.projects-section {
    background: #f8f9fa;
}

.project-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.project-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.project-image {
    position: relative;
    overflow: hidden;
}

.project-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(15, 52, 96, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    opacity: 0;
    transition: all 0.3s ease;
}

.project-image:hover .project-overlay {
    opacity: 1;
}

.project-content {
    padding: 1.5rem;
}

.project-content h4 {
    font-family: 'Pirata One', cursive;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.project-tags {
    margin-top: 1rem;
}

.tag {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

/* Contact Section */
.contact-section {
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    color: var(--text-light);
}

.contact-section .section-title {
    color: var(--text-light);
}

.contact-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.contact-form .form-control {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-light);
    border-radius: 10px;
    padding: 12px 15px;
}

.contact-form .form-control::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

.contact-form .form-control:focus {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--gold-color);
    box-shadow: 0 0 0 0.2rem rgba(255, 215, 0, 0.25);
    color: var(--text-light);
}

.contact-info {
    text-align: center;
    margin-bottom: 2rem;
}

.contact-info i {
    font-size: 2rem;
    color: var(--gold-color);
    margin-bottom: 1rem;
}

.contact-info h5 {
    font-family: 'Pirata One', cursive;
    margin-bottom: 0.5rem;
}

/* Footer */
.footer {
    background: var(--accent-color);
    color: var(--text-light);
}

.social-links a {
    color: var(--text-light);
    font-size: 1.5rem;
    margin-left: 1rem;
    transition: all 0.3s ease;
}

.social-links a:hover {
    color: var(--gold-color);
    transform: translateY(-3px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .stats-row {
        flex-direction: column;
        gap: 1rem;
    }
    
    .floating-ship,
    .treasure-chest {
        display: none;
    }
}

/* Scroll Animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}
