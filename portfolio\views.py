from django.shortcuts import render, redirect
from django.contrib import messages
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import json
from .models import ContactMessage, Project, Skill


def home(request):
    """Home page view"""
    # Get featured projects
    projects = Project.objects.filter(featured=True)[:3]
    # Get skills ordered by proficiency
    skills = Skill.objects.all()[:3]
    
    context = {
        'projects': projects,
        'skills': skills,
    }
    return render(request, 'portfolio/index.html', context)


def about(request):
    """About page view"""
    return render(request, 'portfolio/about.html')


def projects(request):
    """Projects page view"""
    all_projects = Project.objects.all()
    context = {
        'projects': all_projects,
    }
    return render(request, 'portfolio/projects.html', context)


def contact(request):
    """Contact page view"""
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        email = request.POST.get('email', '').strip()
        subject = request.POST.get('subject', '').strip()
        message = request.POST.get('message', '').strip()
        
        # Basic validation
        if not all([name, email, subject, message]):
            messages.error(request, 'Please fill in all fields!')
            return render(request, 'portfolio/contact.html')
        
        if len(name) < 2:
            messages.error(request, 'Please enter a valid name!')
            return render(request, 'portfolio/contact.html')
        
        if len(subject) < 3:
            messages.error(request, 'Subject must be at least 3 characters long!')
            return render(request, 'portfolio/contact.html')
        
        if len(message) < 10:
            messages.error(request, 'Message must be at least 10 characters long!')
            return render(request, 'portfolio/contact.html')
        
        # Save the message
        try:
            ContactMessage.objects.create(
                name=name,
                email=email,
                subject=subject,
                message=message
            )
            messages.success(request, 'Message sent successfully! I\'ll get back to you soon.')
            return redirect('contact')
        except Exception as e:
            messages.error(request, 'An error occurred. Please try again.')
            return render(request, 'portfolio/contact.html')
    
    return render(request, 'portfolio/contact.html')


@csrf_exempt
@require_http_methods(["POST"])
def contact_ajax(request):
    """AJAX contact form submission"""
    try:
        data = json.loads(request.body)
        name = data.get('name', '').strip()
        email = data.get('email', '').strip()
        subject = data.get('subject', '').strip()
        message = data.get('message', '').strip()
        
        # Basic validation
        if not all([name, email, subject, message]):
            return JsonResponse({
                'success': False,
                'message': 'Please fill in all fields!'
            })
        
        if len(name) < 2:
            return JsonResponse({
                'success': False,
                'message': 'Please enter a valid name!'
            })
        
        if len(subject) < 3:
            return JsonResponse({
                'success': False,
                'message': 'Subject must be at least 3 characters long!'
            })
        
        if len(message) < 10:
            return JsonResponse({
                'success': False,
                'message': 'Message must be at least 10 characters long!'
            })
        
        # Save the message
        ContactMessage.objects.create(
            name=name,
            email=email,
            subject=subject,
            message=message
        )
        
        return JsonResponse({
            'success': True,
            'message': 'Message sent successfully! I\'ll get back to you soon.'
        })
        
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': 'An error occurred. Please try again.'
        })
