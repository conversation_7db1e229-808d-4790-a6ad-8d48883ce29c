{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}<PERSON><PERSON> Ku<PERSON>war - Portfolio{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/styles.css' %}">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top aesthetic-nav">
        <div class="container">
            <a class="navbar-brand brand-logo" href="{% url 'portfolio:home' %}">
                <span class="brand-text">Portfolio</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'portfolio:home' %}#home">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'portfolio:home' %}#about">About</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'portfolio:home' %}#skills">Skills</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'portfolio:home' %}#projects">Projects</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'portfolio:contact' %}">Contact</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" style="position: fixed; top: 80px; right: 20px; z-index: 9999; max-width: 400px;">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Main Content -->
    {% block content %}
    {% endblock %}

    <!-- Footer -->
    <footer class="footer py-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p>&copy; 2024 Salina Kunwar. All rights reserved. Crafted with passion.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="social-links">
                        <a href="#"><i class="fab fa-github"></i></a>
                        <a href="#"><i class="fab fa-linkedin"></i></a>
                        <a href="#"><i class="fab fa-twitter"></i></a>
                        <a href="#"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{% static 'js/script.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
