{% extends 'portfolio/base.html' %}
{% load static %}

{% block content %}

    <!-- Hero Section -->
    <section id="home" class="hero-section">
        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <div class="hero-greeting">Hello, I'm</div>
                        <h1 class="hero-title">
                            <span class="typing-text">Salina Kunwar</span>
                        </h1>
                        <h2 class="hero-subtitle">BIT Student & Frontend Developer</h2>
                        <p class="hero-description">
                            Passionate about creating beautiful and intuitive user interfaces.
                            Currently studying at Gandaki University, specializing in frontend
                            technologies and crafting engaging web experiences.
                        </p>
                        <div class="hero-buttons">
                            <a href="#about" class="btn btn-primary-custom btn-lg me-3">
                                <i class="fas fa-user"></i> About Me
                            </a>
                            <a href="#contact" class="btn btn-outline-custom btn-lg">
                                <i class="fas fa-envelope"></i> Get In Touch
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="hero-image">
                        <div class="profile-container">
                            <div class="profile-bg"></div>
                            <div class="floating-elements">
                                <div class="element element-1"></div>
                                <div class="element element-2"></div>
                                <div class="element element-3"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="scroll-indicator">
            <i class="fas fa-chevron-down"></i>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">About Me</h2>
                    <div class="title-underline"></div>
                    <p class="section-subtitle">Get to know me better</p>
                </div>
            </div>
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="about-image">
                        <div class="image-frame">
                            <img src="https://via.placeholder.com/400x400/6366f1/ffffff?text=Salina+Kunwar"
                                 alt="Salina Kunwar" class="img-fluid rounded-circle">
                        </div>
                        <div class="decorative-element">
                            <i class="fas fa-code"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="about-content">
                        <h3 class="about-title">Frontend Developer</h3>
                        <p class="about-text">
                            I'm a dedicated BIT student at Gandaki University with a passion for creating
                            beautiful and functional user interfaces. My journey in web development started with
                            curiosity about how websites work and has evolved into a deep love for crafting
                            engaging digital experiences.
                        </p>
                        <p class="about-text">
                            Specializing in frontend technologies like HTML, CSS, Bootstrap, and JavaScript,
                            I enjoy working on projects that combine aesthetics with functionality. I believe
                            in creating user-centered designs that are both beautiful and accessible.
                        </p>
                        <div class="stats-row">
                            <div class="stat-item">
                                <h4>2+</h4>
                                <p>Years of Study</p>
                            </div>
                            <div class="stat-item">
                                <h4>15+</h4>
                                <p>Projects Completed</p>
                            </div>
                            <div class="stat-item">
                                <h4>∞</h4>
                                <p>Learning Goals</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Skills Section -->
    <section id="skills" class="skills-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">Skills & Technologies</h2>
                    <div class="title-underline"></div>
                    <p class="section-subtitle">Technologies I work with</p>
                </div>
            </div>
            <div class="row">
                {% for skill in skills %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="skill-card">
                        <div class="skill-icon">
                            <i class="{{ skill.icon_class }}"></i>
                        </div>
                        <h4>{{ skill.name }}</h4>
                        <p>{{ skill.description }}</p>
                        <div class="skill-progress">
                            <div class="progress-bar" data-width="{{ skill.proficiency }}%"></div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="skill-card">
                        <div class="skill-icon">
                            <i class="fab fa-js-square"></i>
                        </div>
                        <h4>JavaScript Development</h4>
                        <p>Creating interactive and dynamic web experiences with modern JavaScript and ES6+ features.</p>
                        <div class="skill-progress">
                            <div class="progress-bar" data-width="80%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="skill-card">
                        <div class="skill-icon">
                            <i class="fab fa-html5"></i>
                        </div>
                        <h4>Web Development</h4>
                        <p>Creating responsive and interactive web experiences with HTML, CSS, and Bootstrap.</p>
                        <div class="skill-progress">
                            <div class="progress-bar" data-width="90%"></div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="skill-card">
                        <div class="skill-icon">
                            <i class="fab fa-react"></i>
                        </div>
                        <h4>UI/UX Design</h4>
                        <p>Designing intuitive user interfaces with focus on user experience and accessibility.</p>
                        <div class="skill-progress">
                            <div class="progress-bar" data-width="75%"></div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section id="projects" class="projects-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">Featured Projects</h2>
                    <div class="title-underline"></div>
                    <p class="section-subtitle">Some of my recent work</p>
                </div>
            </div>
            <div class="row">
                {% for project in projects %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="project-card">
                        <div class="project-image">
                            {% if project.image %}
                                <img src="{{ project.image.url }}" alt="{{ project.title }}" class="img-fluid">
                            {% else %}
                                <img src="https://via.placeholder.com/350x200/6366f1/ffffff?text={{ project.title|slice:':10' }}"
                                     alt="{{ project.title }}" class="img-fluid">
                            {% endif %}
                            <div class="project-overlay">
                                {% if project.live_url %}
                                    <a href="{{ project.live_url }}" target="_blank" class="btn btn-primary-custom">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                {% endif %}
                                {% if project.github_url %}
                                    <a href="{{ project.github_url }}" target="_blank" class="btn btn-outline-custom">
                                        <i class="fab fa-github"></i> Code
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                        <div class="project-content">
                            <h4>{{ project.title }}</h4>
                            <p>{{ project.description }}</p>
                            <div class="project-tags">
                                {% for tech in project.get_technologies_list %}
                                    <span class="tag">{{ tech }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="https://via.placeholder.com/350x200/6366f1/ffffff?text=Task+App"
                                 alt="Project 1" class="img-fluid">
                            <div class="project-overlay">
                                <a href="#" class="btn btn-primary-custom">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="#" class="btn btn-outline-custom">
                                    <i class="fab fa-github"></i> Code
                                </a>
                            </div>
                        </div>
                        <div class="project-content">
                            <h4>Task Management App</h4>
                            <p>A responsive task management web application with modern UI design and smooth animations.</p>
                            <div class="project-tags">
                                <span class="tag">HTML/CSS</span>
                                <span class="tag">JavaScript</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="https://via.placeholder.com/350x200/8b5cf6/ffffff?text=Web+Portal"
                                 alt="Project 2" class="img-fluid">
                            <div class="project-overlay">
                                <a href="#" class="btn btn-primary-custom">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="#" class="btn btn-outline-custom">
                                    <i class="fab fa-github"></i> Code
                                </a>
                            </div>
                        </div>
                        <div class="project-content">
                            <h4>University Portal</h4>
                            <p>A responsive student management system with modern design and intuitive interface.</p>
                            <div class="project-tags">
                                <span class="tag">HTML/CSS</span>
                                <span class="tag">Bootstrap</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="project-card">
                        <div class="project-image">
                            <img src="https://via.placeholder.com/350x200/06b6d4/ffffff?text=Dashboard"
                                 alt="Project 3" class="img-fluid">
                            <div class="project-overlay">
                                <a href="#" class="btn btn-primary-custom">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="#" class="btn btn-outline-custom">
                                    <i class="fab fa-github"></i> Code
                                </a>
                            </div>
                        </div>
                        <div class="project-content">
                            <h4>Interactive Dashboard</h4>
                            <p>Modern dashboard interface for visualizing data with interactive charts and animations.</p>
                            <div class="project-tags">
                                <span class="tag">JavaScript</span>
                                <span class="tag">Chart.js</span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title">Get In Touch</h2>
                    <div class="title-underline"></div>
                    <p class="section-subtitle">Let's connect and collaborate</p>
                </div>
            </div>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="contact-form">
                        <form method="post" action="{% url 'portfolio:contact' %}">
                            {% csrf_token %}
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <input type="text" name="name" class="form-control" placeholder="Your Name" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <input type="email" name="email" class="form-control" placeholder="Your Email" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <input type="text" name="subject" class="form-control" placeholder="Subject" required>
                            </div>
                            <div class="mb-3">
                                <textarea name="message" class="form-control" rows="5" placeholder="Your Message" required></textarea>
                            </div>
                            <div class="text-center">
                                <button type="submit" class="btn btn-primary-custom btn-lg">
                                    <i class="fas fa-paper-plane"></i> Send Message
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="row mt-5">
                <div class="col-lg-4 text-center mb-4">
                    <div class="contact-info">
                        <i class="fas fa-map-marker-alt"></i>
                        <h5>Location</h5>
                        <p>Gandaki University<br>Nepal</p>
                    </div>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <div class="contact-info">
                        <i class="fas fa-envelope"></i>
                        <h5>Email</h5>
                        <p><EMAIL></p>
                    </div>
                </div>
                <div class="col-lg-4 text-center mb-4">
                    <div class="contact-info">
                        <i class="fas fa-phone"></i>
                        <h5>Phone</h5>
                        <p>+977 XXX-XXX-XXXX</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

{% endblock %}
